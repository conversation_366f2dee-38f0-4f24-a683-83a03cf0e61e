import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  async rewrites() {
    return [
      // Bosnian localized routes (no prefix)
      {
        source: '/o-nama',
        destination: '/bs/about',
      },
      // German localized routes
      {
        source: '/de/uber-uns',
        destination: '/de/about',
      },
      // Services localized routes
      {
        source: '/usluge',
        destination: '/bs/services',
      },
      {
        source: '/de/dienstleistungen',
        destination: '/de/services',
      },
      // Contact localized routes
      {
        source: '/kontakt',
        destination: '/bs/contact',
      },
      {
        source: '/de/kontakt',
        destination: '/de/contact',
      },
    ];
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
        ],
      },
    ];
  },
};

export default withNextIntl(nextConfig);
