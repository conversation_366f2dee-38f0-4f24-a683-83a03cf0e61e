/* Contact Hero Section - <PERSON> Gradient Animation */

@keyframes contact-hero-gradient-animation {
    0% {
        --c-0: hsla(214.94112800149358, 55%, 20%, 1);
        --x-0: 72%;
        --y-0: 92%;
        --s-start-0: 10%;
        --s-end-0: 62%;
        --c-1: hsla(0, 0%, 0%, 1);
        --x-1: 48%;
        --y-1: 96%;
        --s-start-1: 10%;
        --s-end-1: 62%;
        --c-2: hsla(250.6764221191406, 0%, 0%, 1);
        --x-2: 19%;
        --y-2: 64%;
        --s-start-2: 10%;
        --s-end-2: 62%;
        --c-3: hsla(245.99999999999997, 0%, 0%, 1);
        --x-3: 6%;
        --y-3: 82%;
        --s-start-3: 10%;
        --s-end-3: 62%;
        --c-4: hsla(205.67642211914062, 100%, 43%, 1);
        --x-4: 3%;
        --y-4: 81%;
        --s-start-4: 10%;
        --s-end-4: 62%;
    }

    100% {
        --c-0: hsla(276, 0%, 0%, 1);
        --x-0: 91%;
        --y-0: 98%;
        --s-start-0: 10%;
        --s-end-0: 62%;
        --c-1: hsla(200.3823044720818, 100%, 22%, 1);
        --x-1: 8%;
        --y-1: 5%;
        --s-start-1: 10%;
        --s-end-1: 62%;
        --c-2: hsla(233.00000000000009, 92%, 20%, 1);
        --x-2: 16%;
        --y-2: 72%;
        --s-start-2: 10%;
        --s-end-2: 62%;
        --c-3: hsla(204.31279215802454, 100%, 19%, 1);
        --x-3: 44%;
        --y-3: 36%;
        --s-start-3: 10%;
        --s-end-3: 62%;
        --c-4: hsla(199.0587750603171, 100%, 16%, 1);
        --x-4: 75%;
        --y-4: 26%;
        --s-start-4: 10%;
        --s-end-4: 62%;
    }
}

@property --c-0 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(214.94112800149358, 55%, 20%, 1)
}

@property --x-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 72%
}

@property --y-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 92%
}

@property --s-start-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 62%
}

@property --c-1 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(0, 0%, 0%, 1)
}

@property --x-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 48%
}

@property --y-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 96%
}

@property --s-start-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 62%
}

@property --c-2 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(250.6764221191406, 0%, 0%, 1)
}

@property --x-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 19%
}

@property --y-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 64%
}

@property --s-start-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 62%
}

@property --c-3 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(245.99999999999997, 0%, 0%, 1)
}

@property --x-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 6%
}

@property --y-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 82%
}

@property --s-start-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 62%
}

@property --c-4 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(205.67642211914062, 100%, 43%, 1)
}

@property --x-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 3%
}

@property --y-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

@property --s-start-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 10%
}

@property --s-end-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 62%
}

.contact-hero-gradient-bg {
    --c-0: hsla(214.94112800149358, 55%, 20%, 1);
    --x-0: 72%;
    --y-0: 92%;
    --c-1: hsla(0, 0%, 0%, 1);
    --x-1: 48%;
    --y-1: 96%;
    --c-2: hsla(250.6764221191406, 0%, 0%, 1);
    --x-2: 19%;
    --y-2: 64%;
    --c-3: hsla(245.99999999999997, 0%, 0%, 1);
    --x-3: 6%;
    --y-3: 82%;
    --c-4: hsla(205.67642211914062, 100%, 43%, 1);
    --x-4: 3%;
    --y-4: 81%;
    background-color: hsla(219.59432473437334, 83%, 18%, 1);
    background-image: radial-gradient(circle at var(--x-0) var(--y-0), var(--c-0) var(--s-start-0), transparent var(--s-end-0)), radial-gradient(circle at var(--x-1) var(--y-1), var(--c-1) var(--s-start-1), transparent var(--s-end-1)), radial-gradient(circle at var(--x-2) var(--y-2), var(--c-2) var(--s-start-2), transparent var(--s-end-2)), radial-gradient(circle at var(--x-3) var(--y-3), var(--c-3) var(--s-start-3), transparent var(--s-end-3)), radial-gradient(circle at var(--x-4) var(--y-4), var(--c-4) var(--s-start-4), transparent var(--s-end-4));
    animation: contact-hero-gradient-animation 5s ease-in-out infinite alternate;
    background-blend-mode: normal, normal, normal, normal, normal;
}
