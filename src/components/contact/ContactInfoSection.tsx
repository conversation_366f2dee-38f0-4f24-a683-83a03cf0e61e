'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

const ContactInfoSection = () => {
  const t = useTranslations('contactPage.info');

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation for info cards
  const infoStagger = useStaggeredAnimation(400, 200, 0.3, 1000, 'y');

  const contactInfo = [
    {
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
      ),
      title: t('phone'),
      content: '+387 61 859 534'
    },
    {
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      title: t('email'),
      content: '<EMAIL>'
    },
    {
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      title: t('hours'),
      content: t('hoursText')
    },
    {
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      title: t('response'),
      content: t('responseText')
    }
  ];

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* CSS Gradient Background */}
      <div
        className={`contact-info-gradient-bg absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>
      <div className="grain !opacity-15"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Title Section */}
        <div className="flex flex-col md:flex-row mb-20">
          <div className="md:w-1/3">
            <p className="text-sm text-albatros-ivory font-light">
              <span className="font-bold">Albatros</span><br />
              {t('subtitle')}
            </p>
          </div>
          <div className="md:w-2/3">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-6xl font-normal text-albatros-ivory leading-tight">
                {t('title')}
              </h2>
            </div>
          </div>
        </div>

        {/* Contact Info Grid */}
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/3">
            {/* Empty space for layout */}
          </div>
          <div className="md:w-2/3">
            <div
              ref={infoStagger.ref}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8"
            >
              {contactInfo.map((info, index) => (
                <div
                  key={index}
                  className="opacity-0 translate-y-8 transition-all duration-1000 ease-out"
                >
                  <div className="bg-albatros-ivory/10 backdrop-blur-sm rounded-lg p-8 h-full border border-albatros-ivory/20">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 rounded-full bg-albatros-ivory/20 flex items-center justify-center mr-4 text-albatros-ivory">
                        {info.icon}
                      </div>
                      <h3 className="text-xl font-bold text-albatros-ivory">
                        {info.title}
                      </h3>
                    </div>
                    <p className="text-albatros-ivory/90 leading-relaxed text-lg">
                      {info.content}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactInfoSection;
