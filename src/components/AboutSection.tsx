'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';

const AboutSection = () => {
  const t = useTranslations('hero');
  const features = useTranslations('features');

  // Intersection observers for staggered animations
  const { ref: imageRef, inView: imageInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature1Ref, inView: feature1InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature2Ref, inView: feature2InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature3Ref, inView: feature3InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: bottomRef, inView: bottomInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="md:min-h-screen flex flex-col md:flex-row">
      {/* Left Half - Image */}
      <div
        ref={imageRef}
        className={`w-full md:w-1/2 h-64 md:h-auto relative overflow-hidden transition-all duration-1000 ease-out delay-100 ${
          imageInView
            ? 'opacity-100 scale-100'
            : 'opacity-0 scale-105'
        }`}
      >
        <Image
          src="/images/building.png"
          alt="AlbatrosDoc Building"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 50vw"
        />
        {/* Optional overlay for better text contrast if needed */}
        <div className="absolute inset-0 bg-black/10 z-10"></div>
      </div>

      {/* Right Half - Content */}
      <div className="w-full md:w-1/2 bg-albatros-ivory relative flex items-center justify-center p-6 md:p-12">
        <div className="grain"></div>
        <div className="max-w-2xl h-full md:max-h-[600px] flex flex-col justify-between md:justify-between relative">
          {/* Main Title */}
          <div
            ref={titleRef}
            className={`flex flex-col transition-all duration-1000 ease-out delay-300 ${
              titleInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <h2 className="text-4xl lg:text-5xl mb-4 font-bold text-albatros-black leading-tight">
                {t('promise')}
            </h2>
          </div>

          {/* Features List */}
          <div className="flex flex-col text-xl text-black/80 border-t border-t-black/80">
            <p
              ref={feature1Ref}
              className={`py-3 border-b border-b-black/80 transition-all duration-1000 ease-out delay-500 ${
                feature1InView
                  ? 'opacity-100 translate-x-0'
                  : 'opacity-0 translate-x-8'
              }`}
            >
              {features('fast')}
            </p>
            <p
              ref={feature2Ref}
              className={`py-3 border-b border-b-black/80 transition-all duration-1000 ease-out delay-700 ${
                feature2InView
                  ? 'opacity-100 translate-x-0'
                  : 'opacity-0 translate-x-8'
              }`}
            >
              {features('reliable')}
            </p>
            <p
              ref={feature3Ref}
              className={`py-3 border-b border-b-black/80 transition-all duration-1000 ease-out delay-900 ${
                feature3InView
                  ? 'opacity-100 translate-x-0'
                  : 'opacity-0 translate-x-8'
              }`}
            >
              {features('discrete')}
            </p>
          </div>

          {/* Bottom Text */}
          <div
            ref={bottomRef}
            className={`pt-8 border-t border-albatros-black/10 transition-all duration-1000 ease-out delay-1100 ${
              bottomInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <p className="text-sm text-black leading-tight">
              {t('cta')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
