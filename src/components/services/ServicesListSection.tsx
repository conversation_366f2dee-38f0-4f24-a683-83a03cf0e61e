'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

const ServicesListSection = () => {
  const t = useTranslations('servicesPage.servicesList');

  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation for services
  const firstRowStagger = useStaggeredAnimation(300, 150, 0.3, 1000, 'y');
  const secondRowStagger = useStaggeredAnimation(300, 150, 0.3, 1000, 'y');

  const services = [
    { title: t('services.0.title'), description: t('services.0.description') },
    { title: t('services.1.title'), description: t('services.1.description') },
    { title: t('services.2.title'), description: t('services.2.description') },
    { title: t('services.3.title'), description: t('services.3.description') },
    { title: t('services.4.title'), description: t('services.4.description') },
    { title: t('services.5.title'), description: t('services.5.description') },
    { title: t('services.6.title'), description: t('services.6.description') },
  ];

  return (
    <section className="py-24 relative bg-albatros-ivory">
      <div className="grain"></div>
      <div className="max-w-7xl relative mx-auto px-6 lg:px-8">
        <div className="flex flex-col md:flex-row mb-8 md:mb-20">
          {/* Left 1/3 - Small corner text */}
          <div className="mb-8 md:w-1/3 md:mb-0 pr-12">
            <div className="md:pt-8">
              <p className="text-sm text-black/60 font-light">
                <span className="font-bold">Albatros</span><br />
                {t('subtitle')}
              </p>
            </div>
          </div>

          {/* Right 2/3 - Main title */}
          <div className="md:w-2/3">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-6xl font-normal text-black leading-tight">
                {t('title')}
              </h2>
            </div>
          </div>
        </div>

        {/* Services Grid - 4-3 layout for 7 items */}
        <div className="space-y-8">
          {/* First row - 4 items */}
          <div ref={firstRowStagger.ref} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.slice(0, 4).map((service, index) => (
              <div
                key={index}
                className="opacity-0 translate-y-8 transition-all duration-1000 ease-out"
              >
                <div className="bg-transparent rounded-xl p-8 h-full border border-black transition-all duration-300 hover:shadow-lg">
                  <div className="text-3xl mb-2 text-black font-light">
                    {String(index + 1).padStart(2, '0')}
                  </div>
                  <h3 className="text-xl font-semibold text-black mb-2 leading-tight">
                    {service.title}
                  </h3>
                  <p className="text-black/70 text-sm leading-tight">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Second row - 3 items centered */}
          <div ref={secondRowStagger.ref} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:max-w-4xl lg:mx-auto">
            {services.slice(4, 7).map((service, index) => (
              <div
                key={index + 4}
                className="opacity-0 translate-y-8 transition-all duration-1000 ease-out"
              >
                <div className="bg-transparent rounded-xl p-8 h-full border border-black transition-all duration-300 hover:shadow-lg">
                  <div className="text-3xl mb-2 text-black font-light">
                    {String(index + 5).padStart(2, '0')}
                  </div>
                  <h3 className="text-xl font-semibold text-black mb-2 leading-tight">
                    {service.title}
                  </h3>
                  <p className="text-black/70 text-sm leading-tight">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesListSection;
