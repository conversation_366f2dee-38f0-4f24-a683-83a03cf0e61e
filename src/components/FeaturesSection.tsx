'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import Button from './ui/Button';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

const FeaturesSection = () => {
  const t = useTranslations('hero');
  const services = useTranslations('services');

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });



  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation hooks - matching AboutSection timing and direction
  const servicesStagger = useStaggeredAnimation(500, 100, 0.3, 1000, 'x');
  const napomenaStagger = useStaggeredAnimation(500, 50, 0.3, 1000, 'y');

  const { ref: buttonRef, inView: buttonInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* CSS Gradient Background */}
      <div
        className={`features-gradient-bg absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-1500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>
      <div className="grain !opacity-15"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Mobile Albatros text - shown only on mobile */}
        <div className="md:hidden mb-8">
          <p className="text-sm text-albatros-ivory font-light">
            <span className="font-bold">Albatros</span><br />
              {services('subtitle')}
          </p>
        </div>

        {/* Title Section - Left side only */}
        <div className="flex flex-col md:flex-row mb-16">
          <div className="w-full">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-7xl font-normal text-albatros-ivory">
                {services('title')}
              </h2>
            </div>
          </div>
          {/* Right 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
        </div>

        {/* Services Section - Right side only */}
        <div className="flex flex-col md:flex-row mb-8">
          {/* Left 1/3 - Desktop Albatros text */}
          <div className="md:w-1/3">
            <p className="text-sm text-albatros-ivory font-light hidden md:block">
              <span className="font-bold">Albatros</span><br />
                {services('subtitle')}
            </p>
          </div>
          {/* Right 2/3 - Services List */}
          <div className="md:w-2/3">
            <div
              ref={servicesStagger.ref}
              className="flex flex-col text-xl text-albatros-ivory border-t border-white/20"
            >
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.0')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.1')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.2')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.3')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.4')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.5')}</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-x-8 transition-all duration-1000 ease-out">{services('list.6')}</span>
            </div>
          </div>
        </div>

        {/* Napomena Section - Right side only */}
        <div className="flex flex-col md:flex-row">
          {/* Left 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
          {/* Right 2/3 - Important Notes */}
          <div className="md:w-2/3">
            <div>
              <h3 className="text-lg text-albatros-ivory mb-4 leading-tight">
                {services('noteTitle')}
              </h3>
              <div
                ref={napomenaStagger.ref}
                className="flex flex-col mb-8 font-semibold text-sm text-albatros-ivory/90"
              >
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8 transition-all duration-1000 ease-out">{services('notes.0')}</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8 transition-all duration-1000 ease-out">{services('notes.1')}</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8 transition-all duration-1000 ease-out">{services('notes.2')}</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8 transition-all duration-1000 ease-out">{services('notes.3')}</span>
              </div>
              <div
                ref={buttonRef}
                className={`transition-all duration-1000 ease-out delay-600 ${
                  buttonInView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <Button variant="outlinewhite" size="lg">
                  {t('contactButton')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
