'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

const AboutValuesSection = () => {
  const t = useTranslations('about.values');

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation for values
  const valuesStagger = useStaggeredAnimation(500, 200, 0.3, 1000, 'y');

  return (
    <section ref={backgroundRef} className="py-24 relative overflow-hidden">
      {/* CSS Gradient Background */}
      <div
        className={`features-gradient-bg absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>
      <div className="grain !opacity-25"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Title Section */}
        <div className="flex flex-col md:flex-row mb-12">
          <div className="md:w-1/3 mb-8 md:mb-0">
            <p className="text-sm text-albatros-ivory font-light">
              <span className="font-bold">Albatros</span><br />
              {t('subtitle')}
            </p>
          </div>
          <div className="md:w-2/3">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-6xl font-normal text-albatros-ivory">
                {t('title')}
              </h2>
            </div>
          </div>
        </div>

        {/* Values Grid */}
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/3">
            {/* Empty space for layout */}
          </div>
          <div className="md:w-2/3">
            <div
              ref={valuesStagger.ref}
              className="flex flex-col gap-4"
            >
              {/* Value 1 */}
              <div className="opacity-0 translate-y-8 transition-all duration-1000 ease-out">
                <div className="bg-albatros-ivory/10 backdrop-blur-sm rounded-lg p-8 h-full">
                  <div className="text-albatros-ivory text-xl font-medium mb-4">01</div>
                  <h3 className="text-2xl font-bold text-albatros-ivory mb-4">
                    {t('value1.title')}
                  </h3>
                  <p className="text-albatros-ivory/90 leading-relaxed">
                    {t('value1.description')}
                  </p>
                </div>
              </div>

              {/* Value 2 */}
              <div className="opacity-0 translate-y-8 transition-all duration-1000 ease-out">
                <div className="bg-albatros-ivory/10 backdrop-blur-sm rounded-lg p-8 h-full">
                  <div className="text-albatros-ivory text-xl font-medium mb-4">02</div>
                  <h3 className="text-2xl font-bold text-albatros-ivory mb-4">
                    {t('value2.title')}
                  </h3>
                  <p className="text-albatros-ivory/90 leading-relaxed">
                    {t('value2.description')}
                  </p>
                </div>
              </div>

              {/* Value 3 */}
              <div className="opacity-0 translate-y-8 transition-all duration-1000 ease-out">
                <div className="bg-albatros-ivory/10 backdrop-blur-sm rounded-lg p-8 h-full">
                  <div className="text-albatros-ivory text-xl font-medium mb-4">03</div>
                  <h3 className="text-2xl font-bold text-albatros-ivory mb-4">
                    {t('value3.title')}
                  </h3>
                  <p className="text-albatros-ivory/90 leading-relaxed">
                    {t('value3.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutValuesSection;
