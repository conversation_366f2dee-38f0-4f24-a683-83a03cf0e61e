'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';

const AboutStorySection = () => {
  const t = useTranslations('about.story');

  // Intersection observers for staggered animations
  const { ref: cornerTextRef, inView: cornerTextInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: contentRef, inView: contentInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: missionRef, inView: missionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background with subtle pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-albatros-ivory via-white to-albatros-ivory"></div>
      <div className="grain"></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-16">
          {/* Left side - Title and subtitle */}
          <div className="lg:w-1/2">
            {/* Small subtitle */}
            <div
              ref={cornerTextRef}
              className={`mb-4 transition-all duration-1000 ease-out delay-100 ${
                cornerTextInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <p className="text-sm text-black/60 font-light">
                <span className="font-bold">Albatros</span><br />{t('subtitle')}
              </p>
            </div>

            {/* Large title */}
            <div
              ref={titleRef}
              className={`md:mb-8 lg:mb-0 transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-5xl xl:text-6xl font-normal text-black leading-tight">
                {t('title')}
              </h2>
            </div>
          </div>

          {/* Right side - Content in cards/blocks */}
          <div className="lg:w-1/2 flex flex-col gap-8">
          {/* Story content */}
          <div
            ref={contentRef}
            className={`transition-all duration-1000 ease-out delay-500 ${
              contentInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-albatros-ivory backdrop-blur-sm rounded-2xl p-8 h-full border border-black/5">
              <div className="text-4xl mb-2 text-black/20">01</div>
              <h3 className="text-xl font-semibold text-black mb-4">{t('storyTitle')}</h3>
              <p className="text-black/70 leading-relaxed">
                {t('content')}
              </p>
            </div>
          </div>

          {/* Mission statement */}
          <div
            ref={missionRef}
            className={`transition-all duration-1000 ease-out delay-700 ${
              missionInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-albatros-ivory backdrop-blur-sm rounded-2xl p-8 h-full border border-black/5">
              <div className="text-4xl mb-2 text-black/20">02</div>
              <h3 className="text-xl font-semibold text-black mb-4">{t('missionTitle')}</h3>
              <p className="text-black/70 leading-relaxed font-medium">
                {t('mission')}
              </p>
            </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutStorySection;
